# Serverless Framework configuration for CCTV Monitoring System
# Supports AWS Lambda, Google Cloud Functions, and Azure Functions

service: cctv-monitoring

frameworkVersion: '3'

provider:
  name: aws  # Change to 'google' for GCP or 'azure' for Azure
  runtime: python3.11
  region: us-east-1  # Change to your preferred region
  stage: ${opt:stage, 'dev'}
  timeout: 30  # Maximum timeout for serverless functions
  memorySize: 1024  # Memory allocation (adjust based on model size)
  
  # Environment variables
  environment:
    STAGE: ${self:provider.stage}
    SERVERLESS: true
    FLASK_ENV: production
    FLASK_DEBUG: 0
    TORCH_HOME: /tmp/.cache/torch
    XDG_CACHE_HOME: /tmp/.cache
    CCTV_ADMIN_KEY: cctv_admin_prod_2024
    
  # IAM permissions (for AWS)
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
      Resource: "*"
    - Effect: Allow
      Action:
        - s3:GetObject
        - s3:PutObject
      Resource: "arn:aws:s3:::${self:custom.bucketName}/*"

# Custom variables
custom:
  bucketName: cctv-monitoring-${self:provider.stage}
  
  # Docker configuration
  dockerizePip: true
  
  # Serverless plugins
  pythonRequirements:
    dockerizePip: true
    slim: true
    strip: false
    noDeploy:
      - boto3
      - botocore
      - docutils
      - jmespath
      - python-dateutil
      - s3transfer
      - six
      - pip
      - setuptools

# Functions
functions:
  # Main API handler
  api:
    handler: serverless_handler.handler
    events:
      - http:
          path: /{proxy+}
          method: ANY
          cors: true
      - http:
          path: /
          method: ANY
          cors: true
    timeout: 30
    memorySize: 1024
    
  # Health check function (lightweight)
  health:
    handler: serverless_handler.health_handler
    events:
      - http:
          path: /health
          method: GET
          cors: true
    timeout: 10
    memorySize: 256

# Resources (for AWS)
resources:
  Resources:
    # S3 bucket for model storage
    ModelsBucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: ${self:custom.bucketName}
        PublicAccessBlockConfiguration:
          BlockPublicAcls: true
          BlockPublicPolicy: true
          IgnorePublicAcls: true
          RestrictPublicBuckets: true

# Plugins
plugins:
  - serverless-python-requirements
  - serverless-offline  # For local development

# Package configuration
package:
  patterns:
    - '!node_modules/**'
    - '!.git/**'
    - '!.vscode/**'
    - '!.idea/**'
    - '!__pycache__/**'
    - '!.pytest_cache/**'
    - '!tests/**'
    - '!docs/**'
    - '!examples/**'
    - '!*.md'
    - '!start_server.*'
    - '!docker-compose.yml'
    - '!Dockerfile'
    - '!.dockerignore'

# Alternative configurations for other cloud providers:

# Google Cloud Functions
# provider:
#   name: google
#   runtime: python311
#   project: your-gcp-project-id
#   region: us-central1
#   stage: ${opt:stage, 'dev'}
#   timeout: 60s
#   memorySize: 1024MB
#   environment:
#     SERVERLESS: true
#     FLASK_ENV: production

# Azure Functions
# provider:
#   name: azure
#   runtime: python3.11
#   region: East US
#   stage: ${opt:stage, 'dev'}
#   timeout: PT5M
#   memorySize: 1024
#   environment:
#     SERVERLESS: true
#     FLASK_ENV: production
