#!/usr/bin/env python3
"""
Test script untuk memverifikasi admin key static
"""

import requests
import json
import sys
import os

# Add current directory to path to import admin_config
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from admin_config import AdminConfig

def test_admin_key():
    """Test admin key functionality"""
    
    print("🔑 Testing CCTV Admin Key Configuration")
    print("=" * 45)
    
    # Get admin key
    admin_key = AdminConfig.get_admin_key()
    print(f"Admin Key: {admin_key}")
    print(f"Environment: {os.environ.get('FLASK_ENV', 'development')}")
    print()
    
    # Base URL for API
    base_url = "http://localhost:5000/api/v1"
    
    # Headers with admin key
    headers = {
        'X-API-Key': admin_key,
        'Content-Type': 'application/json'
    }
    
    print("🧪 Running API Tests...")
    print("-" * 25)
    
    # Test 1: Health Check
    try:
        response = requests.get(f"{base_url}/system/health", headers=headers, timeout=5)
        if response.status_code == 200:
            print("✅ Health Check: PASSED")
        else:
            print(f"❌ Health Check: FAILED ({response.status_code})")
    except Exception as e:
        print(f"❌ Health Check: ERROR - {e}")
    
    # Test 2: List API Keys (Admin only)
    try:
        response = requests.get(f"{base_url}/auth/keys", headers=headers, timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                keys_count = len(data.get('data', {}).get('api_keys', []))
                print(f"✅ List API Keys: PASSED ({keys_count} keys found)")
            else:
                print(f"❌ List API Keys: FAILED - {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ List API Keys: FAILED ({response.status_code})")
    except Exception as e:
        print(f"❌ List API Keys: ERROR - {e}")
    
    # Test 3: System Stats (Admin access)
    try:
        response = requests.get(f"{base_url}/system/stats", headers=headers, timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ System Stats: PASSED")
            else:
                print(f"❌ System Stats: FAILED - {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ System Stats: FAILED ({response.status_code})")
    except Exception as e:
        print(f"❌ System Stats: ERROR - {e}")
    
    # Test 4: Create Test API Key
    try:
        test_key_data = {
            "name": "Test Key",
            "permissions": ["read"]
        }
        response = requests.post(f"{base_url}/auth/keys", 
                               headers=headers, 
                               json=test_key_data, 
                               timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                new_key = data.get('data', {}).get('api_key', '')
                print(f"✅ Create API Key: PASSED (Key: {new_key[:20]}...)")
            else:
                print(f"❌ Create API Key: FAILED - {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ Create API Key: FAILED ({response.status_code})")
    except Exception as e:
        print(f"❌ Create API Key: ERROR - {e}")
    
    print()
    print("🔍 Admin Key Validation")
    print("-" * 25)
    
    # Validate admin key format
    if admin_key.startswith('cctv_admin_'):
        print("✅ Key Format: Valid (starts with 'cctv_admin_')")
    else:
        print("❌ Key Format: Invalid (should start with 'cctv_admin_')")
    
    # Check key length
    if len(admin_key) >= 20:
        print(f"✅ Key Length: Valid ({len(admin_key)} characters)")
    else:
        print(f"❌ Key Length: Too short ({len(admin_key)} characters)")
    
    # Check if key is static
    if 'static' in admin_key or admin_key in AdminConfig.get_all_valid_admin_keys():
        print("✅ Key Type: Static key detected")
    else:
        print("⚠️  Key Type: Custom or environment key")
    
    print()
    print("📋 Configuration Summary")
    print("-" * 25)
    print(f"Current Admin Key: {admin_key}")
    print(f"Environment: {os.environ.get('FLASK_ENV', 'development')}")
    print(f"Custom Key Set: {'Yes' if os.environ.get('CCTV_ADMIN_KEY') else 'No'}")
    print(f"Total Valid Keys: {len(AdminConfig.get_all_valid_admin_keys())}")
    
    print()
    print("🚀 Usage Examples")
    print("-" * 17)
    print("curl commands:")
    print(f"  curl -H 'X-API-Key: {admin_key}' http://localhost:5000/api/v1/system/health")
    print(f"  curl -H 'X-API-Key: {admin_key}' http://localhost:5000/api/v1/auth/keys")
    
    print()
    print("Python usage:")
    print(f"  headers = {{'X-API-Key': '{admin_key}'}}")
    print("  response = requests.get('http://localhost:5000/api/v1/system/health', headers=headers)")

def test_all_admin_keys():
    """Test all configured admin keys"""
    
    print("\n🔑 Testing All Admin Keys")
    print("=" * 30)
    
    all_keys = AdminConfig.get_all_valid_admin_keys()
    base_url = "http://localhost:5000/api/v1"
    
    for i, key in enumerate(all_keys, 1):
        print(f"\n{i}. Testing key: {key}")
        
        headers = {'X-API-Key': key}
        
        try:
            response = requests.get(f"{base_url}/system/health", headers=headers, timeout=5)
            if response.status_code == 200:
                print(f"   ✅ Valid and working")
            else:
                print(f"   ❌ Invalid or not working ({response.status_code})")
        except Exception as e:
            print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    print("🔧 CCTV Admin Key Test Script")
    print("=" * 35)
    print()
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:5000/api/v1/system/health", timeout=5)
        print("✅ CCTV Server is running")
        print()
    except Exception as e:
        print("❌ CCTV Server is not running or not accessible")
        print(f"   Error: {e}")
        print()
        print("Please start the server first:")
        print("   python app.py")
        print()
        sys.exit(1)
    
    # Run main test
    test_admin_key()
    
    # Ask if user wants to test all keys
    print()
    try:
        test_all = input("Test all configured admin keys? (y/N): ").lower().strip()
        if test_all == 'y':
            test_all_admin_keys()
    except KeyboardInterrupt:
        print("\nTest cancelled by user.")
    
    print()
    print("✅ Admin key testing completed!")
    print()
    print("💡 Tips:")
    print("   - Use environment variables for production keys")
    print("   - Rotate keys regularly for security")
    print("   - Monitor API usage through logs")
    print("   - Always use HTTPS in production")
