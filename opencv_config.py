#!/usr/bin/env python3
"""
OpenCV Configuration for Serverless Environment
This module configures OpenCV to work properly in headless serverless environments.
"""

import os
import sys

def configure_opencv():
    """Configure OpenCV for serverless/headless environment"""
    
    # Set OpenCV environment variables for headless operation
    os.environ['OPENCV_VIDEOIO_PRIORITY_MSMF'] = '0'
    os.environ['OPENCV_VIDEOIO_PRIORITY_INTEL_MFX'] = '0'
    os.environ['OPENCV_VIDEOIO_PRIORITY_GSTREAMER'] = '0'
    os.environ['OPENCV_VIDEOIO_PRIORITY_V4L2'] = '0'
    os.environ['OPENCV_VIDEOIO_PRIORITY_FFMPEG'] = '1'
    
    # Disable GUI features
    os.environ['QT_QPA_PLATFORM'] = 'offscreen'
    os.environ['DISPLAY'] = ':99'
    
    try:
        import cv2
        print(f"✓ OpenCV {cv2.__version__} loaded successfully")
        
        # Test basic OpenCV functionality
        import numpy as np
        test_img = np.zeros((100, 100, 3), dtype=np.uint8)
        gray = cv2.cvtColor(test_img, cv2.COLOR_BGR2GRAY)
        print("✓ OpenCV basic operations working")
        
        return True
        
    except ImportError as e:
        print(f"✗ Failed to import OpenCV: {e}")
        return False
    except Exception as e:
        print(f"✗ OpenCV configuration error: {e}")
        return False

def test_opencv_features():
    """Test specific OpenCV features needed for the application"""
    try:
        import cv2
        import numpy as np
        
        # Test image processing
        test_img = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        # Test color conversion
        gray = cv2.cvtColor(test_img, cv2.COLOR_BGR2GRAY)
        
        # Test resize
        resized = cv2.resize(test_img, (320, 240))
        
        # Test basic drawing
        cv2.rectangle(test_img, (10, 10), (100, 100), (255, 0, 0), 2)
        
        # Test encoding (important for streaming)
        ret, buffer = cv2.imencode('.jpg', test_img)
        if not ret:
            raise Exception("Failed to encode image")
        
        print("✓ All OpenCV features tested successfully")
        return True
        
    except Exception as e:
        print(f"✗ OpenCV feature test failed: {e}")
        return False

if __name__ == "__main__":
    print("Configuring OpenCV for serverless environment...")
    
    if configure_opencv():
        if test_opencv_features():
            print("✓ OpenCV is ready for serverless deployment")
            sys.exit(0)
        else:
            print("✗ OpenCV feature tests failed")
            sys.exit(1)
    else:
        print("✗ OpenCV configuration failed")
        sys.exit(1)
