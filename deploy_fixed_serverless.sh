#!/bin/bash

# Complete CCTV Serverless Deployment Script with Fixes
# Handles both permission and compatibility issues

set -e

echo "🚀 CCTV Serverless Deployment with Fixes"
echo "========================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if we're in the right directory
if [ ! -f "Dockerfile" ]; then
    echo -e "${RED}❌ Error: Dockerfile not found!${NC}"
    echo "Please run this script from the CCTV project directory."
    exit 1
fi

echo -e "${BLUE}🔧 Fixes included in this deployment:${NC}"
echo "   ✅ Permission fix: TORCH_HOME=/app/.cache/torch"
echo "   ✅ Model compatibility: Updated ultralytics integration"
echo "   ✅ Fallback loading: Multiple model loading strategies"
echo "   ✅ Detection format: Handles both ultralytics and torch.hub results"
echo ""

# Get deployment configuration
echo -e "${YELLOW}📋 Deployment Configuration${NC}"
echo ""

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}❌ gcloud CLI not found!${NC}"
    echo "Please install Google Cloud SDK first."
    exit 1
fi

# Get project ID
PROJECT_ID=$(gcloud config get-value project 2>/dev/null)
if [ -z "$PROJECT_ID" ]; then
    read -p "Enter your Google Cloud Project ID: " PROJECT_ID
    gcloud config set project $PROJECT_ID
fi

# Get region
read -p "Enter region (default: asia-southeast2): " REGION
REGION=${REGION:-asia-southeast2}

# Get service name
read -p "Enter service name (default: cctv-monitoring): " SERVICE_NAME
SERVICE_NAME=${SERVICE_NAME:-cctv-monitoring}

echo ""
echo -e "${BLUE}📋 Deployment Summary:${NC}"
echo "   Project ID: $PROJECT_ID"
echo "   Region: $REGION"
echo "   Service Name: $SERVICE_NAME"
echo ""

# Ask for confirmation
read -p "Do you want to continue with deployment? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Deployment cancelled."
    exit 1
fi

echo ""
echo -e "${GREEN}🔨 Step 1: Building fixed Docker image...${NC}"

# Build the serverless image with all fixes
docker build --target serverless -t cctv-monitoring-serverless-fixed .

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Docker image built successfully!${NC}"
else
    echo -e "${RED}❌ Docker build failed!${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}🧪 Step 2: Testing locally...${NC}"

# Test the image locally
echo "Starting local test container..."
CONTAINER_ID=$(docker run -d -p 8080:8080 -e SERVERLESS=true cctv-monitoring-serverless-fixed)

# Wait for the container to start
echo "Waiting for container to start..."
sleep 15

# Test health endpoint
echo "Testing health endpoint..."
if curl -f http://localhost:8080/api/v1/system/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Health check passed!${NC}"
else
    echo -e "${YELLOW}⚠️  Health check failed, checking container logs...${NC}"
    docker logs $CONTAINER_ID | tail -20
fi

# Test model loading endpoint
echo "Testing model loading..."
RESPONSE=$(curl -s -X POST http://localhost:8080/start_stream \
    -H "Content-Type: application/json" \
    -d '{"stream_url": "test"}' || echo "failed")

if [[ $RESPONSE == *"success"* ]] || [[ $RESPONSE == *"stream_id"* ]]; then
    echo -e "${GREEN}✅ Model loading test passed!${NC}"
    docker stop $CONTAINER_ID > /dev/null 2>&1
elif [[ $RESPONSE == *"failed"* ]]; then
    echo -e "${RED}❌ Model loading test failed!${NC}"
    echo "Container logs:"
    docker logs $CONTAINER_ID | tail -20
    docker stop $CONTAINER_ID > /dev/null 2>&1
    exit 1
else
    echo -e "${YELLOW}⚠️  Model loading test inconclusive, continuing...${NC}"
    docker stop $CONTAINER_ID > /dev/null 2>&1
fi

echo ""
echo -e "${GREEN}📤 Step 3: Pushing to Google Container Registry...${NC}"

# Enable required APIs
echo "Enabling required APIs..."
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com

# Tag for GCR
docker tag cctv-monitoring-serverless-fixed gcr.io/$PROJECT_ID/cctv-monitoring:fixed

# Configure Docker for GCR
gcloud auth configure-docker

# Push the image
docker push gcr.io/$PROJECT_ID/cctv-monitoring:fixed

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Image pushed successfully!${NC}"
else
    echo -e "${RED}❌ Image push failed!${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}🚀 Step 4: Deploying to Cloud Run...${NC}"

# Deploy to Cloud Run with all fixes
gcloud run deploy $SERVICE_NAME \
  --image gcr.io/$PROJECT_ID/cctv-monitoring:fixed \
  --platform managed \
  --region $REGION \
  --allow-unauthenticated \
  --port 8080 \
  --memory 2Gi \
  --cpu 2 \
  --timeout 300 \
  --concurrency 80 \
  --min-instances 0 \
  --max-instances 10 \
  --set-env-vars SERVERLESS=true,FLASK_ENV=production,FLASK_DEBUG=0,TORCH_HOME=/app/.cache/torch,XDG_CACHE_HOME=/app/.cache,CCTV_ADMIN_KEY=cctv_admin_prod_2024

if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}✅ Deployment successful!${NC}"
    
    # Get service URL
    SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region $REGION --format 'value(status.url)')
    
    echo ""
    echo -e "${BLUE}🌐 Service URL: ${SERVICE_URL}${NC}"
    echo ""
    echo -e "${GREEN}🧪 Step 5: Testing deployed service...${NC}"
    
    # Wait for deployment to be ready
    echo "Waiting for deployment to be ready..."
    sleep 20
    
    # Test health endpoint
    echo "Testing health endpoint..."
    if curl -f $SERVICE_URL/api/v1/system/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Health check passed!${NC}"
    else
        echo -e "${YELLOW}⚠️  Health check failed, service might still be starting...${NC}"
    fi
    
    # Test model loading
    echo "Testing model loading endpoint..."
    RESPONSE=$(curl -s -X POST $SERVICE_URL/start_stream \
        -H "Content-Type: application/json" \
        -d '{"stream_url": "test"}' || echo "failed")
    
    if [[ $RESPONSE == *"success"* ]] || [[ $RESPONSE == *"stream_id"* ]]; then
        echo -e "${GREEN}✅ Model loading test passed!${NC}"
        echo ""
        echo -e "${GREEN}🎉 All fixes applied successfully!${NC}"
        echo -e "${BLUE}Your CCTV monitoring system is now running at: ${SERVICE_URL}${NC}"
    elif [[ $RESPONSE == *"torch_load"* ]] || [[ $RESPONSE == *"Permission denied"* ]]; then
        echo -e "${RED}❌ Known issues still present in deployment!${NC}"
        echo "Response: $RESPONSE"
        echo ""
        echo "Please check the logs:"
        echo "gcloud logs read --service $SERVICE_NAME --region $REGION"
    else
        echo -e "${YELLOW}⚠️  Model loading test inconclusive.${NC}"
        echo "Response: $RESPONSE"
        echo ""
        echo "The service might still be starting up. Please check manually:"
        echo "curl -X POST $SERVICE_URL/start_stream -H 'Content-Type: application/json' -d '{\"stream_url\": \"test\"}'"
    fi
    
else
    echo -e "${RED}❌ Deployment failed!${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}✅ Deployment completed!${NC}"
echo ""
echo -e "${BLUE}📝 Summary of fixes applied:${NC}"
echo "   • Set TORCH_HOME=/app/.cache/torch (permission fix)"
echo "   • Set XDG_CACHE_HOME=/app/.cache (permission fix)"
echo "   • Updated model loading to use ultralytics YOLO first"
echo "   • Added torch.hub fallback with force_reload and trust_repo"
echo "   • Updated detection code to handle both result formats"
echo "   • Updated ultralytics to version 8.0.200"
echo ""
echo -e "${BLUE}🔍 If you encounter any issues:${NC}"
echo "   1. Check logs: gcloud logs read --service $SERVICE_NAME --region $REGION"
echo "   2. Test endpoints manually using the service URL above"
echo "   3. Verify memory allocation (2GB allocated)"
echo "   4. Check internet connectivity for model downloads"
echo ""
echo -e "${GREEN}🌐 Access your CCTV system at: ${SERVICE_URL}${NC}"
