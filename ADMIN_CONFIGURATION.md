# CCTV Admin Configuration Guide

## Overview

Sistem CCTV Monitoring sekarang menggunakan **static admin key** untuk akses yang konsisten dan mudah diingat, menggantikan sistem random key generation sebelumnya.

## Static Admin Key

### Default Admin Key
```
cctv_admin_static_key_2024
```

Key ini memberikan akses penuh ke semua fitur admin sistem CCTV monitoring.

## Konfigurasi Admin Key

### 1. Menggunakan Default Static Key

Sistem akan otomatis menggunakan static key default:
```python
# Tidak perlu konfigurasi tambahan
# Key otomatis: cctv_admin_static_key_2024
```

### 2. Menggunakan Environment Variable

Anda dapat override admin key menggunakan environment variable:

```bash
# Set custom admin key
export CCTV_ADMIN_KEY="your_custom_admin_key_here"

# Jalankan aplikasi
python app.py
```

### 3. Environment-Specific Keys

Sistem mendukung key berbeda untuk environment berbeda:

```bash
# Development
export FLASK_ENV=development
# Menggunakan: cctv_admin_dev_2024

# Staging  
export FLASK_ENV=staging
# Menggunakan: cctv_admin_staging_2024

# Production
export FLASK_ENV=production
# Menggunakan: cctv_admin_prod_2024
```

## Penggunaan Admin Key

### 1. API Authentication

```bash
# Contoh penggunaan dengan curl
curl -X GET http://localhost:5000/api/v1/system/health \
  -H "X-API-Key: cctv_admin_static_key_2024"
```

### 2. Membuat Stream Baru

```bash
curl -X POST http://localhost:5000/api/v1/streams \
  -H "X-API-Key: cctv_admin_static_key_2024" \
  -H "Content-Type: application/json" \
  -d '{
    "stream_url": "http://your-stream-url.com/stream.m3u8",
    "name": "Camera 1"
  }'
```

### 3. Mengelola API Keys

```bash
# List semua API keys
curl -X GET http://localhost:5000/api/v1/auth/keys \
  -H "X-API-Key: cctv_admin_static_key_2024"

# Buat API key baru
curl -X POST http://localhost:5000/api/v1/auth/keys \
  -H "X-API-Key: cctv_admin_static_key_2024" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Integration Key",
    "permissions": ["read", "write"]
  }'
```

## Admin Permissions

Admin key memiliki semua permissions:

- `read` - Akses baca ke semua data
- `write` - Akses tulis untuk membuat/update
- `admin` - Akses admin untuk mengelola sistem
- `delete` - Akses hapus untuk menghapus data
- `system` - Akses level sistem

## Security Considerations

### 1. Production Environment

Untuk production, sangat disarankan menggunakan custom admin key:

```bash
# Set custom key untuk production
export CCTV_ADMIN_KEY="your_very_secure_production_key_2024"
export FLASK_ENV=production
```

### 2. Key Rotation

Untuk keamanan maksimal, ganti admin key secara berkala:

```bash
# Ganti key setiap 3-6 bulan
export CCTV_ADMIN_KEY="cctv_admin_$(date +%Y%m)_secure"
```

### 3. Environment Variables

Simpan admin key di environment variables, jangan hardcode di kode:

```bash
# .env file (jangan commit ke git)
CCTV_ADMIN_KEY=your_secure_admin_key_here
FLASK_ENV=production
```

## Troubleshooting

### 1. Admin Key Tidak Bekerja

```bash
# Cek key yang sedang digunakan
python admin_config.py

# Test key dengan curl
curl -X GET http://localhost:5000/api/v1/auth/keys \
  -H "X-API-Key: cctv_admin_static_key_2024" \
  -v
```

### 2. Lupa Admin Key

```bash
# Cek key dari konfigurasi
python -c "from admin_config import AdminConfig; print(AdminConfig.get_admin_key())"

# Atau cek dari log aplikasi
grep "Loaded static admin API key" app.log
```

### 3. Multiple Environment Keys

```bash
# Lihat semua valid keys
python -c "from admin_config import AdminConfig; print(AdminConfig.get_all_valid_admin_keys())"
```

## Migration dari Random Key

Jika Anda sebelumnya menggunakan random generated key:

### 1. Update Client Code

Ganti random key dengan static key:

```python
# Sebelum (random key)
api_key = "cctv_admin_xyz123random"

# Sesudah (static key)  
api_key = "cctv_admin_static_key_2024"
```

### 2. Update Scripts

```bash
# Update semua script yang menggunakan API
sed -i 's/cctv_admin_[a-zA-Z0-9_-]*/cctv_admin_static_key_2024/g' your_script.sh
```

### 3. Update Documentation

Update semua dokumentasi dan contoh kode untuk menggunakan static key.

## Best Practices

1. **Gunakan Environment Variables** untuk production
2. **Jangan commit admin key** ke version control
3. **Rotate key secara berkala** untuk keamanan
4. **Monitor penggunaan key** melalui API logs
5. **Gunakan HTTPS** untuk semua API calls di production

## Contoh Implementasi

### Python Client

```python
import os
from admin_config import AdminConfig

# Get admin key
admin_key = AdminConfig.get_admin_key()

# Use in API client
headers = {
    'X-API-Key': admin_key,
    'Content-Type': 'application/json'
}
```

### Shell Script

```bash
#!/bin/bash

# Load admin key
ADMIN_KEY=$(python -c "from admin_config import AdminConfig; print(AdminConfig.get_admin_key())")

# Use in API calls
curl -X GET http://localhost:5000/api/v1/system/health \
  -H "X-API-Key: $ADMIN_KEY"
```

## Support

Jika mengalami masalah dengan admin configuration:

1. Cek file `admin_config.py` untuk konfigurasi
2. Jalankan `python admin_config.py` untuk debug
3. Periksa environment variables dengan `env | grep CCTV`
4. Cek log aplikasi untuk error authentication
