#!/usr/bin/env python3
"""
Simple script to display current admin key
"""

import os
import sys

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from admin_config import AdminConfig
    
    print("🔑 CCTV Admin Key Information")
    print("=" * 35)
    print()
    
    # Current admin key
    current_key = AdminConfig.get_admin_key()
    print(f"Current Admin Key: {current_key}")
    print()
    
    # Environment info
    env = os.environ.get('FLASK_ENV', 'development')
    print(f"Environment: {env}")
    
    # Custom key check
    custom_key = os.environ.get('CCTV_ADMIN_KEY')
    if custom_key:
        print(f"Custom Key Set: Yes ({custom_key})")
    else:
        print("Custom Key Set: No (using default)")
    
    print()
    print("📋 Usage Examples:")
    print("-" * 18)
    print("API Header:")
    print(f"  X-API-Key: {current_key}")
    print()
    print("Curl Command:")
    print(f"  curl -H 'X-API-Key: {current_key}' http://localhost:5000/api/v1/system/health")
    print()
    print("Python Code:")
    print(f"  headers = {{'X-API-Key': '{current_key}'}}")
    
    print()
    print("🔧 Configuration:")
    print("-" * 16)
    print("To use custom key:")
    print("  export CCTV_ADMIN_KEY='your_custom_key'")
    print()
    print("To set environment:")
    print("  export FLASK_ENV=production")
    
except ImportError as e:
    print(f"Error importing admin_config: {e}")
    print("Using fallback static key...")
    print()
    print("🔑 CCTV Admin Key (Fallback)")
    print("=" * 30)
    print("Admin Key: cctv_admin_static_key_2024")
    print()
    print("Usage:")
    print("  curl -H 'X-API-Key: cctv_admin_static_key_2024' http://localhost:5000/api/v1/system/health")

if __name__ == "__main__":
    pass
