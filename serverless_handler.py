"""
Serverless Handler for CCTV Monitoring System
=============================================

This module provides serverless function handlers for AWS Lambda,
Google Cloud Functions, and Azure Functions.
"""

import json
import os
import sys
import logging
from typing import Dict, Any

# Configure logging for serverless environment
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Set serverless environment
os.environ['SERVERLESS'] = 'true'
os.environ['FLASK_ENV'] = 'production'
os.environ['FLASK_DEBUG'] = '0'

# Set torch cache directory to writable location
os.environ.setdefault('TORCH_HOME', '/app/.cache/torch')
os.environ.setdefault('XDG_CACHE_HOME', '/app/.cache')

try:
    from app import app
    logger.info("Flask app imported successfully")
except Exception as e:
    logger.error(f"Failed to import Flask app: {e}")
    raise

# For AWS Lambda with API Gateway
def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    AWS Lambda handler for API Gateway events
    
    Args:
        event: API Gateway event
        context: Lambda context
        
    Returns:
        API Gateway response
    """
    try:
        # Import serverless WSGI adapter
        try:
            from serverless_wsgi import handle_request
        except ImportError:
            logger.error("serverless_wsgi not installed. Install with: pip install serverless-wsgi")
            return {
                'statusCode': 500,
                'body': json.dumps({'error': 'serverless_wsgi not installed'})
            }
        
        # Handle the request
        response = handle_request(app, event, context)
        return response
        
    except Exception as e:
        logger.error(f"Error in Lambda handler: {e}")
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'error': 'Internal server error',
                'message': str(e)
            })
        }

def health_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    Lightweight health check handler
    
    Args:
        event: API Gateway event
        context: Lambda context
        
    Returns:
        Health check response
    """
    try:
        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'status': 'healthy',
                'timestamp': context.aws_request_id if hasattr(context, 'aws_request_id') else 'unknown',
                'version': '1.0.0'
            })
        }
    except Exception as e:
        logger.error(f"Error in health handler: {e}")
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'status': 'unhealthy',
                'error': str(e)
            })
        }

# For Google Cloud Functions
def gcp_handler(request):
    """
    Google Cloud Functions handler
    
    Args:
        request: Flask request object
        
    Returns:
        Flask response
    """
    try:
        with app.request_context(request.environ):
            return app.full_dispatch_request()
    except Exception as e:
        logger.error(f"Error in GCP handler: {e}")
        return app.response_class(
            response=json.dumps({'error': 'Internal server error', 'message': str(e)}),
            status=500,
            mimetype='application/json'
        )

# For Azure Functions
def azure_handler(req):
    """
    Azure Functions handler
    
    Args:
        req: Azure Functions request object
        
    Returns:
        Azure Functions response
    """
    try:
        import azure.functions as func
        
        # Convert Azure request to WSGI environ
        environ = {
            'REQUEST_METHOD': req.method,
            'PATH_INFO': req.url.split('?')[0].split('/', 3)[-1] if '/' in req.url else '',
            'QUERY_STRING': req.url.split('?')[1] if '?' in req.url else '',
            'CONTENT_TYPE': req.headers.get('content-type', ''),
            'CONTENT_LENGTH': str(len(req.get_body())),
            'wsgi.input': req.get_body(),
            'wsgi.errors': sys.stderr,
            'wsgi.version': (1, 0),
            'wsgi.multithread': False,
            'wsgi.multiprocess': True,
            'wsgi.run_once': False,
            'wsgi.url_scheme': 'https',
        }
        
        # Add headers
        for key, value in req.headers.items():
            key = key.upper().replace('-', '_')
            if key not in ('CONTENT_TYPE', 'CONTENT_LENGTH'):
                environ[f'HTTP_{key}'] = value
        
        # Process request
        with app.request_context(environ):
            response = app.full_dispatch_request()
            
        return func.HttpResponse(
            body=response.get_data(),
            status_code=response.status_code,
            headers=dict(response.headers),
            mimetype=response.mimetype
        )
        
    except Exception as e:
        logger.error(f"Error in Azure handler: {e}")
        import azure.functions as func
        return func.HttpResponse(
            body=json.dumps({'error': 'Internal server error', 'message': str(e)}),
            status_code=500,
            mimetype='application/json'
        )

# For local testing
if __name__ == '__main__':
    # Test the handlers locally
    print("Testing serverless handlers...")
    
    # Test health handler
    test_event = {}
    test_context = type('Context', (), {'aws_request_id': 'test-123'})()
    
    health_response = health_handler(test_event, test_context)
    print(f"Health check response: {health_response}")
    
    # Test main handler (requires serverless_wsgi)
    try:
        test_api_event = {
            'httpMethod': 'GET',
            'path': '/api/v1/system/health',
            'headers': {},
            'queryStringParameters': None,
            'body': None
        }
        
        api_response = handler(test_api_event, test_context)
        print(f"API response: {api_response}")
        
    except ImportError:
        print("serverless_wsgi not installed - skipping API handler test")
    
    print("Handler tests completed")
