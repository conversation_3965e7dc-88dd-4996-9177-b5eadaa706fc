import os

# Server socket
bind = f"0.0.0.0:{os.environ.get('PORT', 8080)}"
backlog = 2048

# Worker processes
workers = 1
worker_class = "gevent"
worker_connections = 1000
timeout = 120
keepalive = 2

# Restart workers
max_requests = 1000
max_requests_jitter = 100

# Logging
accesslog = "-"
errorlog = "-"
loglevel = "info"

# Process naming
proc_name = "cctv-monitoring"

# Server mechanics
preload_app = False
daemon = False
pidfile = "/tmp/gunicorn.pid"
user = "appuser"
group = "appuser"
tmp_upload_dir = None

# SSL (if needed)
keyfile = None
certfile = None
