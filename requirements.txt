# Web Framework
Flask==2.3.3
Werkzeug==2.3.7

# Computer Vision and Image Processing
opencv-python==********
opencv-contrib-python==********

# Machine Learning and Deep Learning
torch==2.0.1
torchvision==0.15.2
ultralytics==8.0.200

# Scientific Computing
numpy==1.24.3
scipy==1.11.3

# Image Processing
Pillow==10.0.1

# HTTP Requests
requests==2.31.0
urllib3==2.0.7

# Data Processing
pandas==2.0.3

# Utilities
python-dateutil==2.8.2
pytz==2023.3

# Development and Debugging
python-dotenv==1.0.0

# Serverless Dependencies
gunicorn==21.2.0
gevent==23.7.0
serverless-wsgi==3.1.0

# Database
PyMySQL==1.1.1


# Optional: GPU Support (uncomment if you have CUDA)
# torch==2.0.1+cu118 --index-url https://download.pytorch.org/whl/cu118
# torchvision==0.15.2+cu118 --index-url https://download.pytorch.org/whl/cu118

# System Dependencies (install via system package manager)
# For Ubuntu/Debian:
# sudo apt-get update
# sudo apt-get install python3-opencv
# sudo apt-get install libglib2.0-0 libsm6 libxext6 libxrender-dev libgl1-mesa-glx

# For Windows:
# Most dependencies should install automatically with pip

# For macOS:
# brew install opencv
