#!/bin/bash

# Fix for CCTV Serverless Permission Error
# This script fixes the "Permission denied: '/home/<USER>'" error in serverless deployments

set -e

echo "🔧 CCTV Serverless Permission Fix"
echo "================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if we're in the right directory
if [ ! -f "Dockerfile" ]; then
    echo -e "${RED}❌ Error: Dockerfile not found!${NC}"
    echo "Please run this script from the CCTV project directory."
    exit 1
fi

echo -e "${BLUE}📋 This script will:${NC}"
echo "   1. Rebuild the Docker image with permission and compatibility fixes"
echo "   2. Update Google Cloud Run deployment (if applicable)"
echo "   3. Test the deployment"
echo ""
echo -e "${BLUE}🔧 Fixes included:${NC}"
echo "   • Permission fix: TORCH_HOME=/app/.cache/torch"
echo "   • Model compatibility: Updated ultralytics integration"
echo "   • Fallback loading: Multiple model loading strategies"
echo ""

# Ask for confirmation
read -p "Do you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Operation cancelled."
    exit 1
fi

echo ""
echo -e "${GREEN}🔨 Step 1: Building fixed Docker image...${NC}"

# Build the serverless image with fixes
docker build --target serverless -t cctv-monitoring-serverless-fixed .

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Docker image built successfully!${NC}"
else
    echo -e "${RED}❌ Docker build failed!${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}🧪 Step 2: Testing locally...${NC}"

# Test the image locally
echo "Starting local test container..."
CONTAINER_ID=$(docker run -d -p 8080:8080 -e SERVERLESS=true cctv-monitoring-serverless-fixed)

# Wait a moment for the container to start
sleep 10

# Test health endpoint
echo "Testing health endpoint..."
if curl -f http://localhost:8080/api/v1/system/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Local test passed!${NC}"
    docker stop $CONTAINER_ID > /dev/null 2>&1
else
    echo -e "${RED}❌ Local test failed!${NC}"
    echo "Container logs:"
    docker logs $CONTAINER_ID
    docker stop $CONTAINER_ID > /dev/null 2>&1
    exit 1
fi

echo ""
echo -e "${YELLOW}🚀 Step 3: Google Cloud Run deployment (optional)${NC}"
read -p "Do you want to deploy to Google Cloud Run? (y/N): " -n 1 -r
echo

if [[ $REPLY =~ ^[Yy]$ ]]; then
    # Check if gcloud is installed
    if ! command -v gcloud &> /dev/null; then
        echo -e "${RED}❌ gcloud CLI not found!${NC}"
        echo "Please install Google Cloud SDK first."
        exit 1
    fi
    
    # Get project ID
    PROJECT_ID=$(gcloud config get-value project 2>/dev/null)
    if [ -z "$PROJECT_ID" ]; then
        read -p "Enter your Google Cloud Project ID: " PROJECT_ID
    fi
    
    # Get region
    read -p "Enter region (default: asia-southeast2): " REGION
    REGION=${REGION:-asia-southeast2}
    
    # Get service name
    read -p "Enter service name (default: cctv-monitoring): " SERVICE_NAME
    SERVICE_NAME=${SERVICE_NAME:-cctv-monitoring}
    
    echo ""
    echo -e "${GREEN}📤 Pushing to Google Container Registry...${NC}"
    
    # Tag for GCR
    docker tag cctv-monitoring-serverless-fixed gcr.io/$PROJECT_ID/cctv-monitoring:fixed
    
    # Configure Docker for GCR
    gcloud auth configure-docker
    
    # Push the image
    docker push gcr.io/$PROJECT_ID/cctv-monitoring:fixed
    
    echo ""
    echo -e "${GREEN}🚀 Deploying to Cloud Run...${NC}"
    
    # Deploy to Cloud Run with fixed environment variables
    gcloud run deploy $SERVICE_NAME \
      --image gcr.io/$PROJECT_ID/cctv-monitoring:fixed \
      --platform managed \
      --region $REGION \
      --allow-unauthenticated \
      --port 8080 \
      --memory 2Gi \
      --cpu 2 \
      --timeout 300 \
      --concurrency 80 \
      --min-instances 0 \
      --max-instances 10 \
      --set-env-vars SERVERLESS=true,FLASK_ENV=production,FLASK_DEBUG=0,TORCH_HOME=/app/.cache/torch,XDG_CACHE_HOME=/app/.cache,CCTV_ADMIN_KEY=cctv_admin_prod_2024
    
    if [ $? -eq 0 ]; then
        echo ""
        echo -e "${GREEN}✅ Deployment successful!${NC}"
        
        # Get service URL
        SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region $REGION --format 'value(status.url)')
        
        echo ""
        echo -e "${BLUE}🌐 Service URL: ${SERVICE_URL}${NC}"
        echo ""
        echo -e "${GREEN}🧪 Testing deployed service...${NC}"
        
        # Test the deployed service
        sleep 15  # Wait for deployment to be ready
        
        if curl -f $SERVICE_URL/api/v1/system/health > /dev/null 2>&1; then
            echo -e "${GREEN}✅ Deployment test passed!${NC}"
            echo ""
            echo -e "${GREEN}🎉 Permission issue fixed successfully!${NC}"
            echo -e "${BLUE}Your CCTV monitoring system is now running at: ${SERVICE_URL}${NC}"
        else
            echo -e "${YELLOW}⚠️  Deployment completed but health check failed.${NC}"
            echo "The service might still be starting up. Please check the logs:"
            echo "gcloud logs read --service $SERVICE_NAME --region $REGION"
        fi
    else
        echo -e "${RED}❌ Deployment failed!${NC}"
        exit 1
    fi
fi

echo ""
echo -e "${GREEN}✅ Fix completed!${NC}"
echo ""
echo -e "${BLUE}📝 Summary of changes:${NC}"
echo "   • Set TORCH_HOME=/app/.cache/torch"
echo "   • Set XDG_CACHE_HOME=/app/.cache"
echo "   • Created writable cache directory in container"
echo "   • Updated all entry points to set environment variables"
echo ""
echo -e "${BLUE}🔍 If you still encounter issues:${NC}"
echo "   1. Check container logs for detailed error messages"
echo "   2. Verify memory allocation (minimum 2GB recommended)"
echo "   3. Ensure internet connectivity for model downloads"
echo ""
