"""
Admin Configuration for CCTV Monitoring System
==============================================

Static configuration for admin credentials and API keys.
"""

import os

class AdminConfig:
    """Static admin configuration"""
    
    # Static Admin API Key
    # This key provides full admin access to the CCTV monitoring system
    STATIC_ADMIN_KEY = "cctv_admin_static_key_2024"
    
    # Alternative admin keys for different environments
    ADMIN_KEYS = {
        'development': 'cctv_admin_dev_2024',
        'staging': 'cctv_admin_staging_2024', 
        'production': 'cctv_admin_prod_2024'
    }
    
    @classmethod
    def get_admin_key(cls):
        """Get admin key based on environment or use static key"""
        # Check environment variable first
        env_key = os.environ.get('CCTV_ADMIN_KEY')
        if env_key:
            return env_key
            
        # Check environment-specific key
        environment = os.environ.get('FLASK_ENV', 'development')
        if environment in cls.ADMIN_KEYS:
            return cls.ADMIN_KEYS[environment]
            
        # Default to static key
        return cls.STATIC_ADMIN_KEY
    
    @classmethod
    def get_all_valid_admin_keys(cls):
        """Get all valid admin keys for validation"""
        keys = [cls.STATIC_ADMIN_KEY]
        keys.extend(cls.ADMIN_KEYS.values())
        
        # Add environment key if set
        env_key = os.environ.get('CCTV_ADMIN_KEY')
        if env_key and env_key not in keys:
            keys.append(env_key)
            
        return keys

# Default admin credentials for web interface (if implemented)
DEFAULT_ADMIN_CREDENTIALS = {
    'username': 'admin',
    'password': 'cctv_admin_2024',  # Change this in production
    'email': '<EMAIL>'
}

# Admin permissions
ADMIN_PERMISSIONS = [
    'read',           # Read access to all data
    'write',          # Write access to create/update
    'admin',          # Admin access to manage system
    'delete',         # Delete access to remove data
    'system'          # System-level access
]

# API rate limits for admin
ADMIN_RATE_LIMITS = {
    'requests_per_minute': 1000,
    'requests_per_hour': 10000,
    'requests_per_day': 100000
}

# Admin notification settings
ADMIN_NOTIFICATIONS = {
    'email_alerts': True,
    'webhook_alerts': True,
    'system_alerts': True,
    'security_alerts': True
}

# Security settings for admin access
ADMIN_SECURITY = {
    'require_https': os.environ.get('REQUIRE_HTTPS', 'false').lower() == 'true',
    'session_timeout_minutes': 60,
    'max_failed_attempts': 5,
    'lockout_duration_minutes': 15,
    'password_min_length': 8,
    'require_strong_password': True
}

# Admin dashboard configuration
ADMIN_DASHBOARD = {
    'refresh_interval_seconds': 5,
    'max_streams_display': 10,
    'show_system_stats': True,
    'show_detection_stats': True,
    'show_performance_metrics': True
}

# Logging configuration for admin actions
ADMIN_LOGGING = {
    'log_admin_actions': True,
    'log_api_usage': True,
    'log_security_events': True,
    'retention_days': 90
}

# Export main admin key for easy import
ADMIN_API_KEY = AdminConfig.get_admin_key()

if __name__ == "__main__":
    print("CCTV Admin Configuration")
    print("=" * 30)
    print(f"Current Admin Key: {AdminConfig.get_admin_key()}")
    print(f"Environment: {os.environ.get('FLASK_ENV', 'development')}")
    print(f"All Valid Keys: {len(AdminConfig.get_all_valid_admin_keys())} keys configured")
    print()
    print("Available Admin Keys:")
    for env, key in AdminConfig.ADMIN_KEYS.items():
        print(f"  {env}: {key}")
    print(f"  static: {AdminConfig.STATIC_ADMIN_KEY}")
    
    env_key = os.environ.get('CCTV_ADMIN_KEY')
    if env_key:
        print(f"  environment: {env_key}")
