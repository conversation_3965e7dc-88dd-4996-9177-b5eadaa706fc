#!/usr/bin/env python3
"""
Test script to verify torch cache directory fix
"""

import os
import sys

def test_torch_cache():
    """Test if torch cache directory is properly configured"""
    
    print("🧪 Testing Torch Cache Configuration")
    print("=" * 40)
    
    # Set environment variables (same as in our fix)
    os.environ.setdefault('TORCH_HOME', '/app/.cache/torch')
    os.environ.setdefault('XDG_CACHE_HOME', '/app/.cache')
    
    print(f"TORCH_HOME: {os.environ.get('TORCH_HOME', 'Not set')}")
    print(f"XDG_CACHE_HOME: {os.environ.get('XDG_CACHE_HOME', 'Not set')}")
    print()
    
    # Test torch import and hub directory
    try:
        import torch
        print("✅ Torch imported successfully")
        
        # Get torch hub directory
        hub_dir = torch.hub.get_dir()
        print(f"📁 Torch hub directory: {hub_dir}")
        
        # Check if directory is writable
        try:
            os.makedirs(hub_dir, exist_ok=True)
            test_file = os.path.join(hub_dir, 'test_write.txt')
            with open(test_file, 'w') as f:
                f.write('test')
            os.remove(test_file)
            print("✅ Torch hub directory is writable")
        except Exception as e:
            print(f"❌ Torch hub directory is not writable: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to import torch: {e}")
        return False
    
    # Test YOLOv5 model loading (multiple approaches)
    try:
        print("\n🤖 Testing YOLOv5 model loading...")

        # Try ultralytics YOLO first
        try:
            from ultralytics import YOLO
            model = YOLO('yolov5s.pt')
            print("✅ YOLOv5s model loaded successfully with ultralytics")

            # Test model inference with dummy data
            import numpy as np
            dummy_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
            results = model(dummy_image)
            print("✅ Model inference test passed with ultralytics")
            return True

        except Exception as e1:
            print(f"⚠️  Ultralytics loading failed: {e1}")
            print("Trying torch.hub as fallback...")

            # Fallback to torch.hub
            try:
                model = torch.hub.load('ultralytics/yolov5', 'yolov5s',
                                     force_reload=True, trust_repo=True, verbose=False)
                print("✅ YOLOv5s model loaded successfully with torch.hub")

                # Test model inference with dummy data
                import numpy as np
                dummy_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
                results = model(dummy_image)
                print("✅ Model inference test passed with torch.hub")
                return True

            except Exception as e2:
                print(f"❌ Both loading methods failed:")
                print(f"   Ultralytics: {e1}")
                print(f"   Torch.hub: {e2}")
                return False

    except Exception as e:
        print(f"❌ YOLOv5 model loading failed: {e}")
        print("This might be due to network connectivity or missing dependencies")
        return False

def test_permissions():
    """Test file permissions in common directories"""
    
    print("\n🔐 Testing File Permissions")
    print("=" * 30)
    
    test_dirs = [
        '/app/.cache',
        '/app/.cache/torch',
        '/app/.cache/torch/hub',
        '/tmp'
    ]
    
    for test_dir in test_dirs:
        try:
            os.makedirs(test_dir, exist_ok=True)
            test_file = os.path.join(test_dir, 'permission_test.txt')
            with open(test_file, 'w') as f:
                f.write('permission test')
            os.remove(test_file)
            print(f"✅ {test_dir} - writable")
        except Exception as e:
            print(f"❌ {test_dir} - not writable: {e}")

if __name__ == "__main__":
    print("🔧 CCTV Serverless Permission Fix - Test Script")
    print("=" * 50)
    print()
    
    # Test permissions first
    test_permissions()
    
    # Test torch cache
    success = test_torch_cache()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed! The permission fix should work.")
    else:
        print("⚠️  Some tests failed. Check the error messages above.")
    
    print("\n💡 To run this test in Docker:")
    print("   docker run --rm cctv-monitoring-serverless python test_torch_cache.py")
