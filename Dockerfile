# Multi-stage build for CCTV Monitoring System
# Optimized for serverless deployment

# Stage 1: Base image with system dependencies
FROM python:3.11-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DEBIAN_FRONTEND=noninteractive \
    OPENCV_VIDEOIO_PRIORITY_MSMF=0 \
    OPENCV_VIDEOIO_PRIORITY_INTEL_MFX=0 \
    OPENCV_VIDEOIO_PRIORITY_GSTREAMER=0

# Install system dependencies for OpenCV and other libraries
RUN apt-get update && apt-get install -y \
    # OpenCV dependencies
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgl1-mesa-dri \
    libgtk-3-0 \
    libgstreamer1.0-0 \
    libgstreamer-plugins-base1.0-0 \
    libavcodec-dev \
    libavformat-dev \
    libswscale-dev \
    libv4l-dev \
    libxvidcore-dev \
    libx264-dev \
    libjpeg-dev \
    libpng-dev \
    libtiff-dev \
    libatlas-base-dev \
    liblapack-dev \
    libeigen3-dev \
    libtheora-dev \
    libvorbis-dev \
    libxine2-dev \
    libopencore-amrnb-dev \
    libopencore-amrwb-dev \
    libv4l-dev \
    libdc1394-dev \
    libavresample-dev \
    # Additional system libraries
    libgomp1 \
    libgcc-s1 \
    # Network and utilities
    curl \
    wget \
    # Clean up
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# Stage 2: Python dependencies
FROM base as dependencies

# Create app directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Stage 3: Application
FROM dependencies as application

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p /app/models \
    && mkdir -p /app/uploads \
    && mkdir -p /app/logs \
    && mkdir -p /app/static/uploads \
    && mkdir -p /app/.cache/torch/hub

# Test OpenCV configuration
RUN python opencv_config.py

# Download YOLOv5 models if not present (for serverless cold starts)
RUN echo "import os\nos.environ.setdefault('TORCH_HOME', '/app/.cache/torch')\nos.environ.setdefault('XDG_CACHE_HOME', '/app/.cache')\n\ntry:\n    from ultralytics import YOLO\n    models = ['yolov5s.pt', 'yolov5m.pt']\n    for model in models:\n        print(f'Downloading {model}...')\n        try:\n            yolo = YOLO(model)\n            print(f'✓ {model} downloaded successfully')\n        except Exception as e:\n            print(f'Warning: Could not download {model}: {e}')\nexcept Exception as e:\n    print(f'Warning: Could not import ultralytics: {e}')\n    print('Models will be downloaded on first run')" > download_models.py && python download_models.py && rm download_models.py

# Set proper permissions
RUN chmod +x start_server.py \
    && chmod 755 /app

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser \
    && chown -R appuser:appuser /app
USER appuser

# Set torch cache directory to writable location
ENV TORCH_HOME=/app/.cache/torch \
    XDG_CACHE_HOME=/app/.cache \
    OPENCV_VIDEOIO_PRIORITY_MSMF=0 \
    OPENCV_VIDEOIO_PRIORITY_INTEL_MFX=0 \
    OPENCV_VIDEOIO_PRIORITY_GSTREAMER=0

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5000/api/v1/system/health || exit 1

# Default command for regular deployment
CMD ["python", "start_server.py"]

# Stage 4: Serverless optimized
FROM application as serverless

# Switch back to root for serverless optimizations
USER root

# Install additional serverless dependencies
RUN pip install --no-cache-dir \
    gunicorn \
    gevent

# Copy serverless entry point
COPY serverless_start.py /app/serverless_start.py
RUN chmod +x /app/serverless_start.py

# Copy gunicorn config for serverless
COPY gunicorn.conf.py /app/gunicorn.conf.py

# Switch back to appuser
RUN chown -R appuser:appuser /app
USER appuser

# Serverless-specific environment variables
ENV SERVERLESS=true \
    FLASK_ENV=production \
    FLASK_DEBUG=0 \
    TORCH_HOME=/app/.cache/torch \
    XDG_CACHE_HOME=/app/.cache \
    CCTV_ADMIN_KEY=cctv_admin_prod_2024 \
    OPENCV_VIDEOIO_PRIORITY_MSMF=0 \
    OPENCV_VIDEOIO_PRIORITY_INTEL_MFX=0 \
    OPENCV_VIDEOIO_PRIORITY_GSTREAMER=0

# Override CMD for serverless
CMD ["python", "serverless_start.py"]
